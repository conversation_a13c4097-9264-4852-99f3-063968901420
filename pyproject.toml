[project]
name = "ai-painting-server"
version = "1.0.0"
description = "AI painting (image generation) service. Input text description, return image URL based on text information."
readme = "README.md"
requires-python = ">=3.10"
dependencies = [ "mcp>=1.11.0",]
[[project.authors]]
name = "peiwy"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[project.scripts]
ai-painting-server = "ai_painting_server:main"

[tool.uv]
[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"

