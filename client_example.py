import subprocess
import json
import sys

# 启动 MCP 服务器（通过 uv run ai-painting-server）
proc = subprocess.Popen(
    ["uv", "--directory", "/private/peiwy/work/custom_mcp_servers", "run", "ai-painting-server"],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# MCP 协议请求示例：listResources
request = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "listResources",
    "params": {
        "prompt": "A cute kitten riding a bicycle, adorable cat with fluffy fur sitting on a colorful bike, sunny day background, cartoon style, cheerful and playful atmosphere, high quality illustration"
    }
}

# 发送请求
proc.stdin.write(json.dumps(request) + "\n")
proc.stdin.flush()

# 读取响应（简单处理，实际 MCP 协议可能有多行/多包）
response = proc.stdout.readline()
print("服务器响应:", response)

# 关闭服务器进程
proc.terminate()
